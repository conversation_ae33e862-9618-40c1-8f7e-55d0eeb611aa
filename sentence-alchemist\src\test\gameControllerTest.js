/**
 * 游戏控制器测试
 * 验证核心功能是否正常工作
 */

import { gameController, GAME_MODE, GAME_STATE } from '../utils/gameController.js';
import { answerValidator } from '../utils/answerValidator.js';
import { learningStateManager } from '../utils/learningStateManager.js';

// 测试游戏控制器基本功能
export function testGameController() {
  console.log('🧪 开始测试游戏控制器...');
  
  try {
    // 测试1: 开始游戏
    console.log('测试1: 开始游戏');
    gameController.startGame(GAME_MODE.PRACTICE);
    const initialState = gameController.getGameState();
    console.log('✅ 游戏状态:', initialState.state);
    console.log('✅ 当前问题:', initialState.currentQuestion?.english);
    
    // 测试2: 提交答案
    console.log('\n测试2: 提交答案');
    if (initialState.currentQuestion && initialState.currentOptions) {
      // 提交正确答案
      gameController.submitAnswer(initialState.correctIndex);
      const afterAnswerState = gameController.getGameState();
      console.log('✅ 答题后状态:', afterAnswerState.state);
      console.log('✅ 答题结果:', afterAnswerState.currentResult?.isCorrect);
      console.log('✅ 得分:', afterAnswerState.currentResult?.score);
    }
    
    // 测试3: 学习状态管理
    console.log('\n测试3: 学习状态管理');
    const sentence = initialState.currentQuestion;
    if (sentence) {
      const learningState = learningStateManager.getLearningState(sentence);
      console.log('✅ 学习状态:', learningState);
      console.log('✅ 掌握程度:', sentence.learningData?.masteryLevel);
    }
    
    // 测试4: 答案验证
    console.log('\n测试4: 答案验证');
    const stats = answerValidator.getValidationStats();
    console.log('✅ 验证统计:', stats);
    
    console.log('\n🎉 所有测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 测试数据分类系统
export function testClassificationSystem() {
  console.log('\n🧪 开始测试数据分类系统...');
  
  try {
    const { createClassifier } = await import('../utils/sentenceClassifier.js');
    const { sampleSentences } = await import('../data/sampleSentences.js');
    
    const classifier = createClassifier(sampleSentences);
    
    // 测试按场景分类
    const airportSentences = classifier.getByScene('airport');
    console.log('✅ 机场场景句子数量:', airportSentences.length);
    
    // 测试按难度分类
    const beginnerSentences = classifier.getByDifficulty('beginner');
    console.log('✅ 初级难度句子数量:', beginnerSentences.length);
    
    // 测试统计信息
    const stats = classifier.getStatistics();
    console.log('✅ 分类统计:', stats);
    
    console.log('🎉 分类系统测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ 分类系统测试失败:', error);
    return false;
  }
}

// 测试干扰项生成
export function testDistractorGeneration() {
  console.log('\n🧪 开始测试干扰项生成...');
  
  try {
    const { generateDistractors, validateDistractors } = await import('../utils/distractorGenerator.js');
    
    const testSentence = {
      english: "Can I have a window seat, please?",
      chinese: "可以给我一个靠窗的座位吗？",
      scene: "airport",
      difficulty: "beginner"
    };
    
    const distractors = generateDistractors(
      testSentence.chinese,
      testSentence.english,
      testSentence.scene,
      testSentence.difficulty
    );
    
    console.log('✅ 生成的干扰项:', distractors);
    
    const isValid = validateDistractors(distractors, testSentence.chinese);
    console.log('✅ 干扰项验证结果:', isValid);
    
    console.log('🎉 干扰项生成测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ 干扰项生成测试失败:', error);
    return false;
  }
}

// 运行所有测试
export function runAllTests() {
  console.log('🚀 开始运行所有测试...\n');
  
  const results = [];
  
  results.push(testGameController());
  results.push(testClassificationSystem());
  results.push(testDistractorGeneration());
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试都通过了！系统运行正常！');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能。');
  }
  
  return passedTests === totalTests;
}

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数添加到全局对象
  window.testGameSystem = {
    runAllTests,
    testGameController,
    testClassificationSystem,
    testDistractorGeneration
  };
  
  console.log('🔧 测试工具已加载！在浏览器控制台中运行 testGameSystem.runAllTests() 来测试系统。');
}
