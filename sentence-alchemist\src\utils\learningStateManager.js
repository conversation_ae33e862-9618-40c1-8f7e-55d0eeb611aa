/**
 * 学习状态管理系统
 * 管理句子的学习状态、复习计划和掌握程度
 */

// 学习状态枚举
export const LEARNING_STATE = {
  NEW: 'new',                    // 新句子
  LEARNING: 'learning',          // 学习中
  REVIEWING: 'reviewing',        // 复习中
  MASTERED: 'mastered',         // 已掌握
  DIFFICULT: 'difficult',       // 困难句子
  FORGOTTEN: 'forgotten'        // 已遗忘
};

// 掌握程度等级
export const MASTERY_LEVEL = {
  UNKNOWN: 0,      // 未知
  POOR: 1,         // 差
  FAIR: 2,         // 一般
  GOOD: 3,         // 良好
  VERY_GOOD: 4,    // 很好
  EXCELLENT: 5     // 优秀
};

// 复习间隔（天数）
export const REVIEW_INTERVALS = {
  [MASTERY_LEVEL.UNKNOWN]: 0,
  [MASTERY_LEVEL.POOR]: 1,
  [MASTERY_LEVEL.FAIR]: 3,
  [MASTERY_LEVEL.GOOD]: 7,
  [MASTERY_LEVEL.VERY_GOOD]: 14,
  [MASTERY_LEVEL.EXCELLENT]: 30
};

/**
 * 学习状态管理器类
 */
export class LearningStateManager {
  constructor() {
    this.stateHistory = new Map(); // 存储每个句子的状态历史
    this.reviewQueue = new Set();  // 复习队列
    this.difficultyQueue = new Set(); // 困难句子队列
  }

  /**
   * 获取句子的当前学习状态
   * @param {Object} sentence - 句子对象
   * @returns {string} 学习状态
   */
  getLearningState(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || learningData.totalAttempts === 0) {
      return LEARNING_STATE.NEW;
    }
    
    const accuracy = learningData.correctCount / learningData.totalAttempts;
    const masteryLevel = learningData.masteryLevel || 0;
    const totalAttempts = learningData.totalAttempts;
    
    // 判断是否需要复习
    if (this.needsReview(sentence)) {
      return LEARNING_STATE.REVIEWING;
    }
    
    // 判断是否已掌握
    if (masteryLevel >= MASTERY_LEVEL.VERY_GOOD && accuracy >= 0.8 && totalAttempts >= 3) {
      return LEARNING_STATE.MASTERED;
    }
    
    // 判断是否困难
    if (accuracy < 0.4 && totalAttempts >= 3) {
      return LEARNING_STATE.DIFFICULT;
    }
    
    // 判断是否遗忘
    if (this.isForgotten(sentence)) {
      return LEARNING_STATE.FORGOTTEN;
    }
    
    // 默认为学习中
    return LEARNING_STATE.LEARNING;
  }

  /**
   * 更新句子的学习状态
   * @param {Object} sentence - 句子对象
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  updateLearningState(sentence, isCorrect, responseTime = 0) {
    // 添加安全检查
    if (!sentence) {
      console.error('updateLearningState: sentence is null or undefined');
      return;
    }

    if (!sentence.learningData) {
      sentence.learningData = {
        correctCount: 0,
        totalAttempts: 0,
        lastReviewed: null,
        nextReview: null,
        masteryLevel: MASTERY_LEVEL.UNKNOWN,
        averageResponseTime: 0,
        recentAccuracy: []
      };
    }

    const learningData = sentence.learningData;

    // 确保recentAccuracy数组存在
    if (!learningData.recentAccuracy) {
      learningData.recentAccuracy = [];
    }

    // 更新基础统计
    learningData.totalAttempts++;
    if (isCorrect) {
      learningData.correctCount++;
    }

    // 更新最近准确率（保留最近10次记录）
    learningData.recentAccuracy.push(isCorrect ? 1 : 0);
    if (learningData.recentAccuracy.length > 10) {
      learningData.recentAccuracy.shift();
    }
    
    // 更新平均响应时间
    if (responseTime > 0) {
      const currentAvg = learningData.averageResponseTime || 0;
      const attempts = learningData.totalAttempts;
      learningData.averageResponseTime = (currentAvg * (attempts - 1) + responseTime) / attempts;
    }
    
    // 更新掌握程度
    this.updateMasteryLevel(sentence, isCorrect, responseTime);
    
    // 更新复习时间
    this.updateReviewSchedule(sentence);
    
    // 更新队列状态
    this.updateQueues(sentence);
    
    // 记录状态历史
    this.recordStateHistory(sentence, isCorrect, responseTime);
  }

  /**
   * 更新掌握程度
   * @param {Object} sentence - 句子对象
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  updateMasteryLevel(sentence, isCorrect, responseTime) {
    const learningData = sentence.learningData;
    const currentLevel = learningData.masteryLevel || MASTERY_LEVEL.UNKNOWN;
    
    if (isCorrect) {
      // 答对了，可能提升掌握程度
      const recentAccuracy = this.getRecentAccuracy(sentence);
      
      if (recentAccuracy >= 0.9 && learningData.totalAttempts >= 3) {
        // 最近表现很好，提升掌握程度
        learningData.masteryLevel = Math.min(MASTERY_LEVEL.EXCELLENT, currentLevel + 1);
      } else if (recentAccuracy >= 0.7 && currentLevel < MASTERY_LEVEL.GOOD) {
        // 表现不错，适度提升
        learningData.masteryLevel = currentLevel + 1;
      }
      
      // 考虑响应时间因素
      if (responseTime > 0) {
        const expectedTime = this.getExpectedResponseTime(sentence);
        if (responseTime > expectedTime * 2) {
          // 响应时间过长，降低提升幅度
          learningData.masteryLevel = Math.max(currentLevel, learningData.masteryLevel - 1);
        }
      }
    } else {
      // 答错了，降低掌握程度
      const recentAccuracy = this.getRecentAccuracy(sentence);
      
      if (recentAccuracy < 0.3) {
        // 最近表现很差，大幅降低
        learningData.masteryLevel = Math.max(MASTERY_LEVEL.UNKNOWN, currentLevel - 2);
      } else if (recentAccuracy < 0.6) {
        // 表现一般，适度降低
        learningData.masteryLevel = Math.max(MASTERY_LEVEL.UNKNOWN, currentLevel - 1);
      }
    }
  }

  /**
   * 更新复习计划
   * @param {Object} sentence - 句子对象
   */
  updateReviewSchedule(sentence) {
    const learningData = sentence.learningData;
    const masteryLevel = learningData.masteryLevel || MASTERY_LEVEL.UNKNOWN;
    
    learningData.lastReviewed = new Date();
    
    // 根据掌握程度设置下次复习时间
    const intervalDays = REVIEW_INTERVALS[masteryLevel] || 1;
    learningData.nextReview = new Date(Date.now() + intervalDays * 24 * 60 * 60 * 1000);
  }

  /**
   * 更新队列状态
   * @param {Object} sentence - 句子对象
   */
  updateQueues(sentence) {
    const state = this.getLearningState(sentence);
    
    // 更新复习队列
    if (state === LEARNING_STATE.REVIEWING || this.needsReview(sentence)) {
      this.reviewQueue.add(sentence.id);
    } else {
      this.reviewQueue.delete(sentence.id);
    }
    
    // 更新困难句子队列
    if (state === LEARNING_STATE.DIFFICULT) {
      this.difficultyQueue.add(sentence.id);
    } else {
      this.difficultyQueue.delete(sentence.id);
    }
  }

  /**
   * 检查句子是否需要复习
   * @param {Object} sentence - 句子对象
   * @returns {boolean} 是否需要复习
   */
  needsReview(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || !learningData.nextReview) {
      return false;
    }
    
    return new Date(learningData.nextReview) <= new Date();
  }

  /**
   * 检查句子是否被遗忘
   * @param {Object} sentence - 句子对象
   * @returns {boolean} 是否被遗忘
   */
  isForgotten(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || !learningData.lastReviewed) {
      return false;
    }
    
    const daysSinceLastReview = (new Date() - new Date(learningData.lastReviewed)) / (1000 * 60 * 60 * 24);
    const masteryLevel = learningData.masteryLevel || MASTERY_LEVEL.UNKNOWN;
    
    // 根据掌握程度判断遗忘阈值
    const forgetThreshold = {
      [MASTERY_LEVEL.UNKNOWN]: 3,
      [MASTERY_LEVEL.POOR]: 7,
      [MASTERY_LEVEL.FAIR]: 14,
      [MASTERY_LEVEL.GOOD]: 30,
      [MASTERY_LEVEL.VERY_GOOD]: 60,
      [MASTERY_LEVEL.EXCELLENT]: 90
    };
    
    return daysSinceLastReview > (forgetThreshold[masteryLevel] || 7);
  }

  /**
   * 获取最近的准确率
   * @param {Object} sentence - 句子对象
   * @returns {number} 最近准确率
   */
  getRecentAccuracy(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || !learningData.recentAccuracy || learningData.recentAccuracy.length === 0) {
      return 0;
    }
    
    const sum = learningData.recentAccuracy.reduce((a, b) => a + b, 0);
    return sum / learningData.recentAccuracy.length;
  }

  /**
   * 获取期望响应时间
   * @param {Object} sentence - 句子对象
   * @returns {number} 期望响应时间（毫秒）
   */
  getExpectedResponseTime(sentence) {
    const difficultyTime = {
      'beginner': 5000,
      'intermediate': 8000,
      'advanced': 12000
    };
    
    return difficultyTime[sentence.difficulty] || 8000;
  }

  /**
   * 记录状态历史
   * @param {Object} sentence - 句子对象
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  recordStateHistory(sentence, isCorrect, responseTime) {
    if (!this.stateHistory.has(sentence.id)) {
      this.stateHistory.set(sentence.id, []);
    }
    
    const history = this.stateHistory.get(sentence.id);
    history.push({
      timestamp: new Date(),
      isCorrect,
      responseTime,
      state: this.getLearningState(sentence),
      masteryLevel: sentence.learningData?.masteryLevel || 0
    });
    
    // 保留最近50条记录
    if (history.length > 50) {
      history.shift();
    }
  }

  /**
   * 获取需要复习的句子
   * @param {Object[]} sentences - 句子数组
   * @returns {Object[]} 需要复习的句子
   */
  getReviewSentences(sentences) {
    return sentences.filter(sentence => this.needsReview(sentence));
  }

  /**
   * 获取困难句子
   * @param {Object[]} sentences - 句子数组
   * @returns {Object[]} 困难句子
   */
  getDifficultSentences(sentences) {
    return sentences.filter(sentence => 
      this.getLearningState(sentence) === LEARNING_STATE.DIFFICULT
    );
  }

  /**
   * 获取学习统计信息
   * @param {Object[]} sentences - 句子数组
   * @returns {Object} 统计信息
   */
  getLearningStats(sentences) {
    const stats = {
      total: sentences.length,
      byState: {},
      byMasteryLevel: {},
      needReview: 0,
      mastered: 0,
      averageMasteryLevel: 0
    };
    
    let totalMasteryLevel = 0;
    
    sentences.forEach(sentence => {
      const state = this.getLearningState(sentence);
      const masteryLevel = sentence.learningData?.masteryLevel || 0;
      
      stats.byState[state] = (stats.byState[state] || 0) + 1;
      stats.byMasteryLevel[masteryLevel] = (stats.byMasteryLevel[masteryLevel] || 0) + 1;
      
      if (this.needsReview(sentence)) {
        stats.needReview++;
      }
      
      if (state === LEARNING_STATE.MASTERED) {
        stats.mastered++;
      }
      
      totalMasteryLevel += masteryLevel;
    });
    
    stats.averageMasteryLevel = sentences.length > 0 ? totalMasteryLevel / sentences.length : 0;
    
    return stats;
  }

  /**
   * 清除所有数据
   */
  clear() {
    this.stateHistory.clear();
    this.reviewQueue.clear();
    this.difficultyQueue.clear();
  }
}

// 导出单例实例
export const learningStateManager = new LearningStateManager();
