import React, { useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { CheckCircle, XCircle } from 'lucide-react';

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const FeedbackContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  margin: 1rem 0;
  border-radius: 12px;
  animation: ${slideIn} 0.3s ease-out;
  min-height: 100px;
  
  &.correct {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
  }
  
  &.incorrect {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
  }
`;

const FeedbackIcon = styled.div`
  margin-bottom: 0.5rem;
  
  svg {
    width: 48px;
    height: 48px;
  }
`;

const FeedbackMessage = styled.h3`
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  text-align: center;
`;

const FeedbackDetail = styled.p`
  font-size: 1rem;
  margin: 0;
  text-align: center;
  opacity: 0.9;
  line-height: 1.4;
`;

const NextButton = styled.button`
  margin-top: 1.5rem;
  padding: 0.8rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const ScoreDisplay = styled.div`
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0.5rem 0;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
`;

const TipsContainer = styled.div`
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  max-width: 500px;
`;

const TipItem = styled.div`
  font-size: 0.9rem;
  margin: 0.5rem 0;
  opacity: 0.9;
  line-height: 1.4;
`;

const FeedbackArea = ({
  isCorrect,
  correctAnswer,
  feedback,
  score,
  onNext,
  autoNext = true,
  autoNextDelay = 2000
}) => {
  // 播放音效的函数
  const playSound = (isCorrect) => {
    // 创建音频上下文来播放简单的音效
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      if (isCorrect) {
        // 正确答案：愉快的音效 (C-E-G 和弦)
        oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
        oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
        oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5
      } else {
        // 错误答案：低沉的音效
        oscillator.frequency.setValueAtTime(220, audioContext.currentTime); // A3
        oscillator.frequency.setValueAtTime(196, audioContext.currentTime + 0.15); // G3
      }
      
      oscillator.type = 'sine';
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.log('Audio not supported or blocked');
    }
  };

  useEffect(() => {
    // 播放音效
    playSound(isCorrect);
    
    // 自动进入下一题
    if (autoNext && onNext) {
      const timer = setTimeout(() => {
        onNext();
      }, autoNextDelay);
      
      return () => clearTimeout(timer);
    }
  }, [isCorrect, autoNext, autoNextDelay, onNext]);

  if (typeof isCorrect !== 'boolean') {
    return null;
  }

  return (
    <FeedbackContainer className={isCorrect ? 'correct' : 'incorrect'}>
      <FeedbackIcon>
        {isCorrect ? <CheckCircle /> : <XCircle />}
      </FeedbackIcon>

      <FeedbackMessage>
        {feedback?.message || (isCorrect ? '正确！' : '答错了')}
      </FeedbackMessage>

      {score && (
        <ScoreDisplay>
          得分: {score}
        </ScoreDisplay>
      )}

      {!isCorrect && correctAnswer && (
        <FeedbackDetail>
          正确答案是：{correctAnswer}
        </FeedbackDetail>
      )}

      {feedback?.explanation && (
        <FeedbackDetail>
          {feedback.explanation}
        </FeedbackDetail>
      )}

      {feedback?.encouragement && isCorrect && (
        <FeedbackDetail>
          {feedback.encouragement}
        </FeedbackDetail>
      )}

      {feedback?.tips && feedback.tips.length > 0 && (
        <TipsContainer>
          <div style={{ fontWeight: '600', marginBottom: '0.5rem' }}>学习提示：</div>
          {feedback.tips.map((tip, index) => (
            <TipItem key={index}>• {tip}</TipItem>
          ))}
        </TipsContainer>
      )}

      {!autoNext && (
        <NextButton onClick={onNext}>
          下一题
        </NextButton>
      )}
    </FeedbackContainer>
  );
};

export default FeedbackArea;
